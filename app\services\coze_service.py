# -*- coding: utf-8 -*-
"""
Coze API封装

Coze API文档: https://www.coze.cn/open/docs/developer_guides/chat_v3

提供与Coze空间API交互的功能，包括文件上传、运行coze智能体、查询结果等。
智能体主页: https://www.coze.cn/space/7533538568590278708/bot/7545030128452452406
"""

import aiohttp
import os
from typing import Optional, List, Callable, Dict, Any
import json
import asyncio
import time
# from datetime import datetime  # 不再需要，已使用 logger 替代
from urllib.parse import urljoin
from pathlib import Path

# 导入日志记录器
from ..core.logger import get_logger

# 获取日志记录器
logger = get_logger()

# Coze API 配置常量
# 一个月换一次, token过期会报401错误
COZE_TOKEN = "pat_Q8htISRjqmREUPXEijFWPuHH5dMBcfXUJfrFHbP3Y8Ub1Ie8netzm0FVeCRiUV0s"

# 水平衡服务业表
WB_SERVICE_TABLE_BOT_1 = "7545026832061055022"  # 上传文件智能体
WB_SERVICE_TABLE_BOT_2 = "7545030128452452406"  # 生成文档智能体

# 水平衡工业业表
WB_INDUSTRY_TABLE_BOT_1 = "7547603282953273396"  # 上传文件智能体
WB_INDUSTRY_TABLE_BOT_2 = "7547604333807370286"  # 生成文档智能体

# 图片文件扩展名
IMAGE_EXTENSIONS = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.tiff', '.svg'}

# Coze API 端点常量
CHAT_URL = "https://api.coze.cn/v3/chat"
MESSAGE_LIST_URL = "https://api.coze.cn/v3/chat/message/list"

async def upload_file(token: str, file_path: str) -> Optional[Dict[str, Any]]:
    '''
    调用接口上传文件到coze.
    如果成功, 就返回字典{"type": file_type, "file_id": file_id}, 在后续任务中, coze内部流程可以通过file_id找到对应的文件.

curl --location --request POST 'https://api.coze.cn/v1/files/upload' \
--header 'Authorization: Bearer sat_cjVlORG4J9x54NomG9fm9RKm0GXoXQ40QL1XEV22fm1XOK31inZLBmHdWDcwScnZ' \
--header 'Content-Type: multipart/form-data' \
--form 'file=@"/home/<USER>/projects/lcmp.server/www/static/tmp/t5ik028109a4c08c.docx"'

curl return:
{"code":0,"data":{"bytes":202959,"created_at":1755582817,"file_name":"t5ik028109a4c08c.docx","id":"7540170131218579499"},"detail":{"logid":"20250819135337BD88EDDC9E88E108BC48"},"msg":""}

Important reutrned json fields:
code: Long. 调用状态码。0 表示调用成功，其他值表示调用失败，你可以通过 msg 字段判断详细的错误原因。
msg: String. 状态信息。API 调用失败时可通过此字段查看详细错误信息。

https://www.coze.cn/open/docs/developer_guides/upload_files#返回参数
    '''

    # 检查文件是否存在
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"文件不存在: {file_path}")

    # API端点
    url = "https://api.coze.cn/v1/files/upload"

    # 请求头
    headers = {
        'Authorization': f'Bearer {token}'
    }

    try:
        async with aiohttp.ClientSession(connector=aiohttp.TCPConnector()) as session:
            # 准备文件数据
            with open(file_path, 'rb') as file:
                # 获取文件名
                file_name = os.path.basename(file_path)

                # 创建multipart/form-data
                data = aiohttp.FormData()
                data.add_field('file', file, filename=file_name)

                # 发送POST请求
                async with session.post(url, headers=headers, data=data) as response:
                    result = await response.json()

                    if response.status == 200:
                        # 检查返回的code字段
                        code = result.get('code')
                        if code == 0:
                            # 调用成功，返回文件ID
                            file_id = result.get('data', {}).get('id')
                            if file_id:
                                file_ext = os.path.splitext(file_name)[1].lower()
                                file_type = "image" if file_ext in IMAGE_EXTENSIONS else "file"
                                return {"type": file_type, "file_id": file_id}
                            else:
                                error_msg = f"返回错误的file_id: {result}"
                                logger.error(error_msg)
                                raise Exception(error_msg)
                        else:
                            # 调用失败，打印日志并抛出异常
                            error_msg = result.get('msg', '未知错误')
                            logger.error(f"文件上传失败 - coze code: {code}, msg: {error_msg}")
                            raise Exception(f"文件上传失败 - coze code: {code}, msg: {error_msg}")
                    else:
                        logger.error(f"文件上传HTTP异常 code: {response.status}")

    except aiohttp.ClientError as e:
        logger.error(f"网络请求错误: {str(e)}")
        raise Exception(f"网络请求错误: {str(e)}")
    except Exception as e:
        logger.error(f"文件上传异常: {str(e)}")
        raise Exception(f"文件上传异常: {str(e)}")


async def call_chat_agent(token: str, bot_id: str, user_id: str, files_list: List[Dict[str, Any]]) -> Dict[str, str]:
    '''
    调用智能体接口发起会话.

curl --location --request POST 'https://api.coze.cn/v3/chat' \
--header 'Authorization: Bearer sat_cjVlORG4J9x54NomG9fm9RKm0GXoXQ40QL1XEV22fm1XOK31inZLBmHdWDcwScnZ' \
--header 'Content-Type: application/json' \
--data-raw '{
    "bot_id": "7545030128452452406",
    "user_id": "lcmp123456789",
    "stream": false,
    "auto_save_history":true,
    "additional_messages":[
        {
            "role":"user",
            "content":"[{\"type\":\"file\",\"file_id\":\"7540170131218579499\"}]",
            "content_type":"object_string"
        }
    ]
}'

curl return:
{"data":{"id":"7540171039872548916","conversation_id":"7540171039872483380","bot_id":"7545030128452452406","created_at":1755582878,"last_error":{"code":0,"msg":""},"status":"in_progress"},"code":0,"msg":""}

Important reutrned json fields:
code: Long. 调用状态码。0 表示调用成功，其他值表示调用失败，你可以通过 msg 字段判断详细的错误原因。
msg: String. 状态信息。API 调用失败时可通过此字段查看详细错误信息。
data.id: String. 即chat_id.
data.conversation_id: String. 会话id.

Function return:
return an dict of {"conversation_id": conversation_id, "id": id} if everything is OK, else print logs and raise error.
    '''

    # API端点
    url = "https://api.coze.cn/v3/chat"

    # 请求头
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }

    # 请求体
    payload = {
        "bot_id": bot_id,
        "user_id": user_id,
        "stream": False,
        "auto_save_history": True,
        "additional_messages": [
            {
                "role": "user",
                "content": json.dumps(files_list),
                "content_type": "object_string"
            }
        ]
    }

    try:
        async with aiohttp.ClientSession(connector=aiohttp.TCPConnector()) as session:
            async with session.post(url, headers=headers, json=payload) as response:
                result = await response.json()

                if response.status == 200:
                    # 检查返回的code字段
                    code = result.get('code')
                    if code == 0:
                        # 调用成功，返回conversation_id和chat_id
                        data = result.get('data', {})
                        conversation_id = data.get('conversation_id')
                        chat_id = data.get('id')

                        if conversation_id and chat_id:
                            return {"conversation_id": conversation_id, "id": chat_id}
                        else:
                            error_msg = f"返回数据缺少必要字段: {result}"
                            logger.error(error_msg)
                            raise Exception(error_msg)
                    else:
                        # 调用失败，打印日志并抛出异常
                        error_msg = result.get('msg', '未知错误')
                        logger.error(f"智能体调用失败 - coze code: {code}, msg: {error_msg}")
                        raise Exception(f"智能体调用失败 - coze code: {code}, msg: {error_msg}")
                else:
                    error_msg = f"智能体调用HTTP异常 - status: {response.status}"
                    logger.error(error_msg)
                    raise Exception(error_msg)

    except aiohttp.ClientError as e:
        logger.error(f"网络请求错误: {str(e)}")
        raise Exception(f"网络请求错误: {str(e)}")
    except Exception as e:
        logger.error(f"智能体调用异常: {str(e)}")
        raise Exception(f"智能体调用异常: {str(e)}")


async def send_chat_request(token: str, bot_id: str, user_id: str, content: any, content_type: str = "text") -> Optional[tuple[str, str]]:
    """
    发送聊天请求并返回chat_id和conversation_id

    根据 coze_example.py 中的逻辑重写的异步版本，用于发送聊天请求到 Coze API。

    Args:
        token: Coze API 访问令牌
        bot_id: 机器人ID
        user_id: 用户ID
        content: 消息内容，可以是任意类型
        content_type: 内容类型，默认为"text"，可选"object_string"

    Returns:
        Optional[tuple[str, str]]: 成功时返回(chat_id, conversation_id)元组，失败时返回None

    Raises:
        Exception: 当API调用失败或网络错误时抛出异常
    """
    # 构建消息内容
    if content_type == "object_string":
        content = json.dumps(content)

    # 请求负载
    payload = {
        "bot_id": bot_id,
        "user_id": user_id,
        "stream": False,
        "auto_save_history": True,
        "additional_messages": [{
            "role": "user",
            "content": content,
            "content_type": content_type
        }]
    }

    # 请求头配置
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }

    try:
        # 发送异步HTTP请求
        async with aiohttp.ClientSession(connector=aiohttp.TCPConnector()) as session:
            async with session.post(CHAT_URL, headers=headers, json=payload) as response:
                # 检查HTTP状态码
                if response.status != 200:
                    error_text = await response.text()
                    logger.error(f"聊天请求失败: {response.status} - {error_text}")
                    return None

                # 解析JSON响应
                result = await response.json()

                # 检查API返回码
                if result.get('code') != 0:
                    logger.error(f"API错误: {result.get('msg')}")
                    return None

                # 提取返回数据
                data = result.get('data', {})
                chat_id = data.get('id')
                conversation_id = data.get('conversation_id')

                if chat_id and conversation_id:
                    logger.info(f"聊天请求成功 - chat_id: {chat_id}, conversation_id: {conversation_id}")
                    return chat_id, conversation_id
                else:
                    logger.error(f"返回数据缺少必要字段: {result}")
                    return None

    except Exception as e:
        logger.error(f"发送聊天请求出错: {str(e)}")
        return None

async def peek_conversion_status(token: str, conversation_id: str, chat_id: str) -> Optional[Dict[str, Any]]:
    '''
    查询对话是否结束.

curl --location --request GET 'https://api.coze.cn/v3/chat/retrieve?conversation_id=7540171039872483380&chat_id=7540171039872548916' \
--header 'Authorization: Bearer sat_cjVlORG4J9x54NomG9fm9RKm0GXoXQ40QL1XEV22fm1XOK31inZLBmHdWDcwScnZ' \
--header 'Content-Type: application/json'

curl return:
进行中:
{
  "code": 0,
  "data": {
    "bot_id": "7545030128452452406",
    "conversation_id": "7545401820030615604",
    "created_at": 1756800764,
    "id": "7545401820030648372",
    "status": "in_progress"
  },
  "detail": {
    "logid": "20250902161928DAA2B21EE78FB42C6339"
  },
  "msg": ""
}
已完成:
{
  "code": 0,
  "data": {
    "bot_id": "7545030128452452406",
    "completed_at": 1756801175,
    "conversation_id": "7545401820030615604",
    "created_at": 1756800764,
    "id": "7545401820030648372",
    "status": "completed",
    "usage": {
      "input_count": 1150,
      "input_tokens_details": {
        "cached_tokens": 0
      },
      "output_count": 1051,
      "output_tokens_details": {
        "reasoning_tokens": 0
      },
      "token_count": 145562
    }
  },
  "detail": {
    "logid": "20250902161938F461009D7D38C108A386"
  },
  "msg": ""
}

Important reutrned json fields:
code: Long. 调用状态码。0 表示调用成功，其他值表示调用失败，你可以通过 msg 字段判断详细的错误原因。
msg: String. 状态信息。API 调用失败时可通过此字段查看详细错误信息。
data.status: String. 为"completed"时表示会话已结束.

Function return:
return {"status": data.status, "result": result} if code is 0, else print logs and raise error.

注意: 通过"https://api.coze.cn/v3/chat/retrieve"这个api只能查询到任务是否完成, 但不能返回任务结果.
     返回任务结果要调用"https://api.coze.cn/v3/chat/message/list", 见get_processed_content函数.
    '''

    # API端点
    url = f"https://api.coze.cn/v3/chat/retrieve?conversation_id={conversation_id}&chat_id={chat_id}"

    # 请求头
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }

    try:
        async with aiohttp.ClientSession(connector=aiohttp.TCPConnector()) as session:
            async with session.get(url, headers=headers) as response:
                logger.debug(f"peek_conversion_status response: {response}")
                result = await response.json()

                if response.status == 200:
                    # 检查返回的code字段
                    code = result.get('code')
                    if code == 0:
                        # 调用成功，获取状态
                        data = result.get('data', {})
                        status = data.get('status')

                        # 如果状态为completed，计算并打印消耗时间
                        if status == 'completed':
                            completed_at = data.get('completed_at')
                            created_at = data.get('created_at')

                            if completed_at and created_at:
                                # 计算消耗时间（单位：秒）
                                duration = completed_at - created_at
                                logger.info(f"对话已完成，消耗时间: {duration} 秒")

                        return {"status": status, "result": result}
                    else:
                        # 调用失败，打印日志并抛出异常
                        error_msg = result.get('msg', '未知错误')
                        logger.error(f"查询对话状态失败 - coze code: {code}, msg: {error_msg}")
                        raise Exception(f"查询对话状态失败 - coze code: {code}, msg: {error_msg}")
                else:
                    error_msg = f"查询对话状态HTTP异常 - status: {response.status}"
                    logger.error(error_msg)
                    raise Exception(error_msg)

    except aiohttp.ClientError as e:
        logger.error(f"网络请求错误: {str(e)}")
        raise Exception(f"网络请求错误: {str(e)}")
    except Exception as e:
        logger.error(f"查询对话状态异常: {str(e)}")
        raise Exception(f"查询对话状态异常: {str(e)}")

async def check_conversion_completed(
    token: str,
    conversation_id: str,
    chat_id: str,
    interval: int = 10,
    timeout: int = 1200,
    callback: Optional[Callable[[Dict[str, Any]], None]] = None,
    errback: Optional[Callable[[Exception], None]] = None
) -> Dict[str, Any]:
    '''
在interval间隔内循环调用peek_conversion_status, 查看对话是否结束. 如果结束, 就调用callback回调, 如果遇到错误, 就调用errback回调.

参数:
interval: int, 查询间隔事件, 默认5, 单位秒.
timeout: int, 查询总超时时间, 默认600, 单位秒, 如果超时, 也调用errback(传入一个表示超时的异常对象).
callback: 函数, 可选参数, 调用callback不需要传入参数.
errback: 函数, 可选参数 调用errback传入异常对象.

函数返回: Dict[str, Any] - 包含任务状态和结果的字典，格式为 {"TaskStatus": "状态", "Result": "结果"}.
    '''
    start_time = time.time()
    try:
        while True:
            # 检查是否超时
            elapsed_time = time.time() - start_time
            if elapsed_time >= timeout:
                timeout_error = TimeoutError(f"查询对话状态超时，已等待 {elapsed_time:.1f} 秒")
                logger.warning(f"查询对话状态超时: {elapsed_time:.1f} 秒")
                if errback:
                    errback(timeout_error)
                return {"TaskStatus": "TIMEOUT", "Result": str(timeout_error)}

            try:
                # 调用peek_conversion_status查询状态
                response = await peek_conversion_status(token, conversation_id, chat_id)

                # 从响应中提取状态信息
                status = response.get('status') if response else None
                result = response.get('result') if response else None

                # 打印响应结果
                logger.info(f"查询响应结果 - 对话状态: {status}")
                if result:
                    logger.debug(f"完整响应数据: {json.dumps(result, ensure_ascii=False, indent=2)}")
                # 如果状态为completed，调用callback并返回
                if status == 'completed':
                    logger.info(f"对话已完成, 消耗时间: {elapsed_time:.1f}秒, 运行结果: {result}")
                    if callback:
                        callback(result)
                    return {"TaskStatus": "COMPLETED", "Result": result}
                # 如果状态不是completed，等待interval秒后继续查询
                logger.info(f"对话状态: {status}, {interval}秒后重新查询...")
                await asyncio.sleep(interval)
            except Exception as e:
                # 如果查询过程中出现异常，调用errback
                logger.error(f"查询对话状态时发生异常: {str(e)}")
                if errback:
                    errback(e)
                return {"TaskStatus": "Error", "Result": str(e)}
    except Exception as e:
        # 捕获其他未预期的异常
        logger.error(f"check_conversion_completed发生未预期异常: {str(e)}")
        if errback:
            errback(e)
        return {"TaskStatus": "Error", "Result": str(e)}

async def get_processed_content(token: str, conversation_id: str, chat_id: str,
                               max_retries: int = 60, retry_interval: int = 30) -> Optional[str]:
    """
    获取处理后的内容

    根据 coze_example.py 中的逻辑重写的异步版本，用于获取聊天对话中包含 reasoning_content 的消息内容。

    Args:
        token: Coze API 访问令牌
        conversation_id: 对话ID
        chat_id: 聊天ID
        max_retries: 最大重试次数，默认60次
        retry_interval: 重试间隔时间（秒），默认30秒, 共30分钟

    Returns:
        Optional[str]: 处理后的内容，如果未找到则返回None

    Raises:
        Exception: 当API调用失败或网络错误时抛出异常
    """
    logger.info(f"开始获取处理结果，最多重试 {max_retries} 次，每次间隔 {retry_interval} 秒...")

    # 请求头配置
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }

    # 重试循环
    for attempt in range(max_retries):
        try:
            logger.info(f"\nConcersation {conversation_id} retry {attempt + 1}/{max_retries}...")

            # 构建请求URL
            url = f"{MESSAGE_LIST_URL}?chat_id={chat_id}&conversation_id={conversation_id}"

            # 发送异步HTTP请求
            async with aiohttp.ClientSession(connector=aiohttp.TCPConnector()) as session:
                async with session.get(url, headers=headers) as response:
                    # 打印响应状态码
                    logger.debug(f"Peek processed content, status code: {response.status}")

                    # 检查HTTP状态码
                    if response.status != 200:
                        error_text = await response.text()
                        logger.error(f"获取消息失败: {response.status} - {error_text}")
                        if attempt < max_retries - 1:
                            logger.info(f"等待 {retry_interval} 秒后重试...")
                            await asyncio.sleep(retry_interval)
                        continue

                    # 解析JSON响应
                    result = await response.json()
                    # {'code': 0, 'detail': {'logid': '20250904115937A493CD031E5BEE228BD3'}, 'msg': ''}
                    logger.info(f"Peek processed content, response data: {result}")

                    # 检查API返回码
                    if result.get('code') != 0:
                        logger.error(f"API错误: {result.get('msg')}")
                        if attempt < max_retries - 1:
                            logger.info(f"等待 {retry_interval} 秒后重试...")
                            await asyncio.sleep(retry_interval)
                        continue

                    # 处理消息数据
                    messages = result.get('data', []) # messages是一个列表
                    for msg in messages:
                        # 查找包含 reasoning_content 的消息
                        if 'reasoning_content' in msg: # 在messages列表中找到含有reasoning_content的项, 然后找出其content
                            content = msg.get('content', '')
                            if content:
                                logger.info("\033[1;33m成功获取到处理内容\033[0m")
                                return content

                    # 未找到目标消息
                    logger.warning("未找到包含 reasoning_content 的消息")
                    if attempt < max_retries - 1:
                        logger.info(f"等待 {retry_interval} 秒后重试...")
                        await asyncio.sleep(retry_interval)

        except Exception as e:
            logger.error(f"获取消息出错: {str(e)}")
            if attempt < max_retries - 1:
                logger.info(f"等待 {retry_interval} 秒后重试...")
                await asyncio.sleep(retry_interval)

    # 所有重试都失败
    logger.error(f"\033[1;31;40m在 {max_retries} 次尝试后未找到处理内容\033[0m")
    return None

async def handle_coze_doc_request(request_body: Dict[str, Any]) -> Dict[str, Any]:
    """
    处理Coze文档请求的主要逻辑函数

    Args:
        request_body: 包含TaskKey、Callback、Contents的请求体字典

    Returns:
        Dict[str, Any]: 包含TaskKey、RuntimeStatus、TaskStatus、Data的响应字典
    """
    task_key = request_body.get("TaskKey")
    callback_url = request_body.get("Callback")
    project_type = request_body.get("ProjType")
    contents = request_body.get("Contents", [])

    bot_id_1= WB_SERVICE_TABLE_BOT_1
    bot_id_2 = WB_SERVICE_TABLE_BOT_2
    if project_type == "ServiceTable":
        bot_id_1 = WB_SERVICE_TABLE_BOT_1
        bot_id_2 = WB_SERVICE_TABLE_BOT_2
    elif project_type == "IndustryTable":
        bot_id_1 = WB_INDUSTRY_TABLE_BOT_1
        bot_id_2 = WB_INDUSTRY_TABLE_BOT_2
    else:
        bot_id_1 = WB_SERVICE_TABLE_BOT_1
        bot_id_2 = WB_SERVICE_TABLE_BOT_2

    logger.info(f"Coze文档请求, TaskKey: {task_key}, 项目类型: {project_type}, 智能体1: {bot_id_1[0:3]}***{bot_id_1[-4:]}, 智能体2: {bot_id_2[0:3]}***{bot_id_2[-4:]}")

    try:
        # 1. 创建临时目录
        temp_dir = Path(f"tmp/coze/{task_key}")
        temp_dir.mkdir(parents=True, exist_ok=True)

        # 2. 下载文件到本地临时目录
        downloaded_files = []
        for uri in contents:
            try:
                # 构建完整的HTTP地址 (这里需要从请求中获取主机信息)
                # 暂时使用默认主机，实际应用中需要从请求头获取
                file_url = f"http://127.0.0.1:5577{uri}"

                # 获取文件名
                filename = Path(uri).name
                local_file_path = temp_dir / filename

                # 下载文件
                async with aiohttp.ClientSession(connector=aiohttp.TCPConnector()) as session:
                    async with session.get(file_url) as response:
                        if response.status == 200:
                            with open(local_file_path, 'wb') as f:
                                async for chunk in response.content.iter_chunked(8192):
                                    f.write(chunk)
                            downloaded_files.append(str(local_file_path))
                            logger.info(f"文件下载成功: {filename}")
                        else:
                            logger.error(f"文件下载失败: {filename}, 状态码: {response.status}")

            except Exception as e:
                logger.error(f"下载文件失败 {uri}: {str(e)}")
                continue

        if not downloaded_files:
            return {
                "Code": -1,
                "TaskKey": task_key,
                "RuntimeStatus": "FAILED",
                "TaskStatus": "FAILED",
                "Data": {"error": "没有成功下载任何文件"}
            }

        # 3. 上传文件到Coze空间
        uploaded_files = []
        for file_path in downloaded_files:
            try:
                result = await upload_file(COZE_TOKEN, file_path)
                if result:
                    uploaded_files.append(result)
                    logger.info(f"文件上传成功: {Path(file_path).name}")
                else:
                    logger.error(f"文件上传失败: {Path(file_path).name}")
            except Exception as e:
                logger.error(f"上传文件失败 {file_path}: {str(e)}")
                continue

        if not uploaded_files:
            return {
                "Code": -1,
                "TaskKey": task_key,
                "RuntimeStatus": "FAILED",
                "TaskStatus": "FAILED",
                "Data": {"error": "没有成功上传任何文件到Coze"}
            }

        # 4. 按照 process_folder 逻辑处理文件：先用 BOT_1，再用 BOT_2
        try:
            # 4.1 发送聊天请求到第一个智能体 (BOT_1)
            logger.info("开始调用第一个智能体 (BOT_1)...")
            chat_info_1 = await send_chat_request(COZE_TOKEN, bot_id_1, task_key, uploaded_files, "object_string")
            if not chat_info_1:
                return {
                    "Code": -1,
                    "TaskKey": task_key,
                    "RuntimeStatus": "FAILED",
                    "TaskStatus": "FAILED",
                    "Data": {"error": "第一个智能体聊天请求失败"}
                }

            chat_id_1, conversation_id_1 = chat_info_1
            logger.info(f"第一个智能体对话创建成功: conversation_id={conversation_id_1}, chat_id={chat_id_1}")

            # 4.2 获取第一个智能体的处理结果
            logger.info("等待第一个智能体处理完成...")
            content_1 = await get_processed_content(COZE_TOKEN, conversation_id_1, chat_id_1)
            if not content_1:
                return {
                    "Code": -1,
                    "TaskKey": task_key,
                    "RuntimeStatus": "FAILED",
                    "TaskStatus": "FAILED",
                    "Data": {"error": "未能获取第一个智能体的处理结果"}
                }

            logger.info("第一个智能体处理完成，开始调用第二个智能体 (BOT_2)...")

            # 4.3 发送第一个智能体的输出到第二个智能体 (BOT_2)
            chat_info_2 = await send_chat_request(COZE_TOKEN, bot_id_2, task_key, content_1, "text")
            if not chat_info_2:
                return {
                    "Code": -1,
                    "TaskKey": task_key,
                    "RuntimeStatus": "FAILED",
                    "TaskStatus": "FAILED",
                    "Data": {"error": "第二个智能体聊天请求失败"}
                }

            chat_id_2, conversation_id_2 = chat_info_2
            logger.info(f"第二个智能体对话创建成功: conversation_id={conversation_id_2}, chat_id={chat_id_2}")

            # 4.4 获取第二个智能体的处理结果
            logger.info("等待第二个智能体处理完成...")
            final_content = await get_processed_content(COZE_TOKEN, conversation_id_2, chat_id_2)
            if not final_content:
                return {
                    "Code": -1,
                    "TaskKey": task_key,
                    "RuntimeStatus": "FAILED",
                    "TaskStatus": "FAILED",
                    "Data": {"error": "未能获取第二个智能体的处理结果"}
                }

            logger.info("\033[1;33m两个智能体处理完成！\033[0m")
            logger.info(f"最终处理结果, TaskKey: {task_key}, content: {final_content}")

            # 使用第二个智能体的结果作为最终对话信息（用于回调）
            conversation_id = conversation_id_2
            chat_id = chat_id_2

        except Exception as e:
            # 调用错误回调函数
            error_callback(e)

            return {
                "Code": -1,
                "TaskKey": task_key,
                "RuntimeStatus": "FAILED",
                "TaskStatus": "FAILED",
                "Data": {"error": f"智能体处理失败: {str(e)}"}
            }

        # 5. 定义回调函数
        def success_callback(result):
            """成功回调函数"""
            if callback_url:
                async def send_callback():
                    try:
                        # 将第二个智能体的处理结果加入到返回数据中
                        enhanced_result = {}
                        enhanced_result["TaskKey"] = task_key
                        enhanced_result["TaskStatus"] = "COMPLETED"
                        enhanced_result["RuntimeStatus"] = "SUCCESS"
                        enhanced_result["Content"] = result

                        callback_data = {
                            "Data": enhanced_result
                        }

                        async with aiohttp.ClientSession(connector=aiohttp.TCPConnector()) as session:
                            # 手动序列化 JSON 以保持中文字符不被转义
                            json_data = json.dumps(callback_data, ensure_ascii=False)
                            headers = {'Content-Type': 'application/json; charset=utf-8'}
                            async with session.post(str(callback_url), data=json_data.encode('utf-8'), headers=headers) as response:
                                if response.status == 200:
                                    logger.info(f"回调成功: {callback_url}")
                                else:
                                    logger.error(f"回调失败: {callback_url}, 状态码: {response.status}")

                    except Exception as e:
                        logger.error(f"发送回调请求失败: {str(e)}")

                # 创建异步任务发送回调
                asyncio.create_task(send_callback())

        def error_callback(error):
            """错误回调函数"""
            if callback_url:
                async def send_error_callback():
                    try:
                        enhanced_result = {}
                        enhanced_result["TaskKey"] = task_key
                        enhanced_result["TaskStatus"] = "FAILED"
                        enhanced_result["RuntimeStatus"] = "FAILED",
                        enhanced_result["Error"] = str(error)

                        callback_data = {
                            "Data": enhanced_result
                        }

                        async with aiohttp.ClientSession(connector=aiohttp.TCPConnector()) as session:
                            # 手动序列化 JSON 以保持中文字符不被转义
                            json_data = json.dumps(callback_data, ensure_ascii=False)
                            headers = {'Content-Type': 'application/json; charset=utf-8'}
                            async with session.post(str(callback_url), data=json_data.encode('utf-8'), headers=headers) as response:
                                if response.status == 200:
                                    logger.info(f"错误回调成功: {callback_url}")
                                else:
                                    logger.error(f"错误回调失败: {callback_url}, 状态码: {response.status}")

                    except Exception as e:
                        logger.error(f"发送错误回调请求失败: {str(e)}")

                # 创建异步任务发送错误回调
                asyncio.create_task(send_error_callback())

        # 6. 调用成功回调函数
        # 将第二个智能体的处理结果作为回调数据
        success_callback(final_content)

        # 7. 返回初始响应
        return {
            "Code": 0,
            "TaskKey": task_key,
            "RuntimeStatus": "SUCCESS",
            "TaskStatus": "COMPLETED",
            "Data": final_content
        }

    except Exception as e:
        logger.error(f"处理Coze文档请求失败: {str(e)}")
        # 调用错误回调函数
        error_callback(e)

        return {
            "Code": -1,
            "TaskKey": task_key,
            "RuntimeStatus": "FAILED",
            "TaskStatus": "FAILED",
            "Data": {"error": str(e)}
        }

    finally:
        # 清理临时文件
        try:
            if 'temp_dir' in locals() and temp_dir.exists():
                import shutil
                shutil.rmtree(temp_dir)
                logger.info(f"临时目录已清理: {temp_dir}")
        except Exception as e:
            logger.error(f"清理临时目录失败: {str(e)}")
