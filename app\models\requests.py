# -*- coding: utf-8 -*-
"""
API请求数据模型

定义所有API端点的请求数据模型，使用Pydantic进行数据验证。
"""

import re
from typing import Optional, Annotated, Any, List, Literal, Dict
from pydantic import BaseModel, Field, field_validator, HttpUrl, ValidationError, ConfigDict

from enum import Enum


class TingwuRequest(BaseModel):
    """
    千义听悟音频转写请求数据模型

    用于验证创建音频转写任务的请求参数。
    """

    # 必需字段
    TaskKey: Annotated[str, Field(
        min_length=1,
        max_length=100,
        pattern=r'^[a-zA-Z0-9_-]+$',
        description="任务唯一标识符，只能包含字母、数字、下划线和连字符，长度1-100字符",
        examples=["task_tingwu_123"]
    )]

    fileUrl: HttpUrl = Field(
        ...,
        description="音频文件的HTTP或HTTPS URL地址",
        examples=["http://example.com/audio.mp3"]
    )

    # 可选字段
    sourceLanguage: Optional[Literal[
        'cn', 'en', 'ja', 'ko', 'es', 'fr', 'de', 'it', 'pt', 'ru'
    ]] = Field(
        default="cn",
        description="源语言代码，支持中文(cn)、英文(en)、日文(ja)、韩文(ko)、西班牙文(es)、法文(fr)、德文(de)、意大利文(it)、葡萄牙文(pt)、俄文(ru)",
        examples=["cn", "en"]
    )

    sampleRate: Optional[Annotated[int, Field(ge=8000, le=48000)]] = Field(
        default=16000,
        description="音频采样率，范围8000-48000Hz",
        examples=[16000, 44100]
    )

    speakerCount: Optional[Annotated[int, Field(ge=0, le=20)]] = Field(
        default=0,
        description="说话人数量，范围0-20人，0表示自动检测",
        examples=[0, 2, 3]
    )

    outputLevel: Optional[Annotated[int, Field(ge=1, le=3)]] = Field(
        default=1,
        description="输出详细级别，1=基础，2=标准，3=详细",
        examples=[1, 2, 3]
    )

    diarizationEnabled: Optional[bool] = Field(
        default=True,
        description="是否启用说话人分离功能"
    )

    progressiveCallbacksEnabled: Optional[bool] = Field(
        default=True,
        description="是否启用渐进式回调通知"
    )

    translationEnabled: Optional[bool] = Field(
        default=False,
        description="是否启用翻译功能"
    )

    autoChaptersEnabled: Optional[bool] = Field(
        default=False,
        description="是否启用自动章节分割"
    )

    meetingAssistanceEnabled: Optional[bool] = Field(
        default=False,
        description="是否启用会议辅助功能"
    )

    summarizationEnabled: Optional[bool] = Field(
        default=False,
        description="是否启用内容摘要功能"
    )

    Prompts: Optional[List[dict]] = Field(
        default=None,
        description="自定义Prompt内容列表",
        examples=[[{"name": "concat-dialogs", "prompt": "把对话内容连接成一整段文本:{Transcription}"}]]
    )

    @field_validator('TaskKey')
    @classmethod
    def validate_task_key(cls, v: str) -> str:
        """验证TaskKey格式"""
        if not v:
            raise ValueError('TaskKey不能为空')

        if not re.match(r'^[a-zA-Z0-9_-]+$', v):
            raise ValueError('TaskKey只能包含字母、数字、下划线和连字符')

        if len(v) > 100:
            raise ValueError('TaskKey长度不能超过100个字符')

        return v

    @field_validator('fileUrl')
    @classmethod
    def validate_file_url(cls, v: HttpUrl) -> HttpUrl:
        """验证文件URL格式"""
        if not v:
            raise ValueError('fileUrl不能为空')

        # 转换为字符串进行进一步验证
        url_str = str(v)

        # 检查是否为HTTP或HTTPS协议
        if not url_str.startswith(('http://', 'https://')):
            raise ValueError('fileUrl必须是HTTP或HTTPS协议的URL')

        return v


class ChatRequest(BaseModel):
    """
    大模型文本交互请求数据模型

    用于验证创建聊天任务的请求参数。
    """

    # 可选字段
    Model: Optional[str] = Field(
        default="qwen-plus",
        description="使用的大模型名称",
        examples=["qwen-plus", "qwen-turbo"]
    )

    SysContent: str = Field(
        ...,
        min_length=1,
        max_length=100000,
        description="系统角色的内容，定义AI助手的行为和角色",
        examples=["你是一个专业的AI助手"]
    )

    UserContent: str = Field(
        ...,
        min_length=1,
        max_length=600000,
        description="用户角色的内容，用户的问题或指令",
        examples=["你好，请介绍一下自己"]
    )

    @field_validator('SysContent', 'UserContent')
    @classmethod
    def validate_content(cls, v: str) -> str:
        """验证内容不能为空"""
        if not v or not v.strip():
            raise ValueError('内容不能为空')
        return v.strip()


class DocxgenRequest(BaseModel):
    """
    Docx文档生成请求数据模型

    用于验证创建docx文档生成任务的请求参数，包含必需字段和文档内容定义。
    """

    # 必需字段
    TaskKey: Annotated[str, Field(
        min_length=1,
        max_length=100,
        pattern=r'^[a-zA-Z0-9_-]+$',
        description="任务唯一标识符，只能包含字母、数字、下划线和连字符，长度1-100字符",
        examples=["docx_task_20240101_001"]
    )]

    Callback: HttpUrl = Field(
        ...,
        description="回调URL地址，用于任务完成后的通知",
        examples=["http://127.0.0.1:5577/api/v1/aihub/docxgen/new/callback"]
    )

    Contents: List[Any] = Field(
        ...,
        min_length=1,
        description="文档内容定义，数组格式，描述文档的结构和元素",
        examples=[["Cover",[{"Type":"enter","useTemp":False,"fontSize":"小一"}]]]
    )

    @field_validator('TaskKey')
    @classmethod
    def validate_task_key(cls, v: str) -> str:
        """
        验证TaskKey格式

        Args:
            v: TaskKey值

        Returns:
            str: 验证通过的TaskKey

        Raises:
            ValueError: 当TaskKey格式不符合要求时
        """
        if not v:
            raise ValueError('TaskKey不能为空')

        if not v.replace('_', '').replace('-', '').isalnum():
            raise ValueError('TaskKey只能包含字母、数字、下划线和连字符')

        if len(v) > 100:
            raise ValueError('TaskKey长度不能超过100个字符')

        return v

    @field_validator('Callback')
    @classmethod
    def validate_callback_url(cls, v: HttpUrl) -> HttpUrl:
        """
        验证回调URL格式

        Args:
            v: 回调URL

        Returns:
            HttpUrl: 验证通过的URL

        Raises:
            ValueError: 当URL格式不符合要求时
        """
        if not v:
            raise ValueError('Callback不能为空')

        # 转换为字符串进行进一步验证
        url_str = str(v)

        # 检查是否为HTTP或HTTPS协议
        if not url_str.startswith(('http://', 'https://')):
            raise ValueError('Callback必须是HTTP或HTTPS协议的URL')

        return v

    @field_validator('Contents')
    @classmethod
    def validate_contents(cls, v: List[Any]) -> List[Any]:
        """
        验证Contents格式

        Args:
            v: Contents数组

        Returns:
            List[Any]: 验证通过的Contents

        Raises:
            ValueError: 当Contents格式不符合要求时
        """
        if not v:
            raise ValueError('Contents不能为空')

        if not isinstance(v, list):
            raise ValueError('Contents必须是数组格式')

        if len(v) == 0:
            raise ValueError('Contents数组不能为空')

        return v

    model_config = ConfigDict(
        # 允许使用字段别名
        populate_by_name=True,
        # 验证赋值
        validate_assignment=True,
        # 使用枚举值而不是枚举名称
        use_enum_values=True,
        # JSON编码配置
        json_encoders={
            # 可以在这里添加自定义编码器
        },
        # 示例配置
        json_schema_extra={
            "example": {
                "TaskKey": "docx_task_20240101_001",
                "Callback": "http://127.0.0.1:5577/api/v1/aihub/docxgen/new/callback",
                "Contents": [["Cover",[{"Type":"enter","useTemp":False,"fontSize":"小一"}]]]
            }
        }
    )


# 验证函数
def validate_tingwu_request(data: dict) -> TingwuRequest:
    """
    验证千义听悟请求数据

    Args:
        data (dict): 请求数据字典

    Returns:
        TingwuRequest: 验证通过的请求模型实例

    Raises:
        ValidationError: 当数据验证失败时
    """
    return TingwuRequest(**data)


def validate_chat_request(data: dict) -> ChatRequest:
    """
    验证聊天请求数据

    Args:
        data (dict): 请求数据字典

    Returns:
        ChatRequest: 验证通过的请求模型实例

    Raises:
        ValidationError: 当数据验证失败时
    """
    return ChatRequest(**data)


class CozeDocRequest(BaseModel):
    """
    Coze文档处理请求数据模型

    用于验证创建Coze文档处理任务的请求参数。
    """

    # 必需字段
    TaskKey: Annotated[str, Field(
        min_length=1,
        max_length=100,
        pattern=r'^[a-zA-Z0-9_-]+$',
        description="任务唯一标识符，只能包含字母、数字、下划线和连字符，长度1-100字符",
        examples=["coze_task_20240101_001"]
    )]

    ProjType: Annotated[str, Field(
        min_length=1,
        max_length=100,
        pattern=r'^[a-zA-Z0-9_-]+$',
        description="项目类型，用于选取BOT_ID, 只能包含字母、数字、下划线和连字符，长度1-100字符",
        examples=["IndustryTable", "ServiceTable"]
    )]

    Callback: HttpUrl = Field(
        ...,
        description="回调URL地址，用于任务完成后的通知",
        examples=["http://127.0.0.1:5577/api/v1/coze/doc/callback"]
    )

    Contents: List[str] = Field(
        ...,
        min_length=1,
        description="文件URI列表，每个元素是一个文件的相对路径",
        examples=["/assets/proj/2025/03/phk5rp9cahskqltlv8/fattach/PHK5R1756689404983.docx"]
    )

    @field_validator('TaskKey')
    @classmethod
    def validate_task_key(cls, v: str) -> str:
        """
        验证任务键格式

        Args:
            v: 任务键值

        Returns:
            str: 验证后的任务键

        Raises:
            ValueError: 任务键格式不正确时抛出
        """
        if not re.match(r'^[a-zA-Z0-9_-]+$', v):
            raise ValueError('TaskKey只能包含字母、数字、下划线和连字符')
        return v

    @field_validator('Callback')
    @classmethod
    def validate_callback_url(cls, v: HttpUrl) -> HttpUrl:
        """
        验证回调URL格式

        Args:
            v: 回调URL

        Returns:
            HttpUrl: 验证后的URL

        Raises:
            ValueError: URL格式不正确时抛出
        """
        url_str = str(v)
        if not (url_str.startswith('http://') or url_str.startswith('https://')):
            raise ValueError('Callback必须是有效的HTTP或HTTPS URL')
        return v

    @field_validator('Contents')
    @classmethod
    def validate_contents(cls, v: List[str]) -> List[str]:
        """
        验证文件URI列表

        Args:
            v: 文件URI列表

        Returns:
            List[str]: 验证后的URI列表

        Raises:
            ValueError: URI格式不正确时抛出
        """
        if not v:
            raise ValueError('Contents不能为空')

        for uri in v:
            if not isinstance(uri, str):
                raise ValueError('Contents中的每个元素必须是字符串')
            if not uri.startswith('/'):
                raise ValueError('文件URI必须以"/"开头')

        return v

    # 模型配置
    model_config = ConfigDict(
        # 允许使用字段别名
        populate_by_name=True,
        # 启用赋值验证
        validate_assignment=True,
        # 使用枚举值而不是枚举对象
        use_enum_values=True,
        # JSON编码器
        json_encoders={
            # HttpUrl类型转换为字符串
        },
        # JSON Schema额外信息
        json_schema_extra={
            "example": {
                "TaskKey": "coze_task_20240101_001",
                "Callback": "http://127.0.0.1:5577/api/v1/coze/doc/callback",
                "Contents": ["/assets/proj/2025/03/phk5rp9cahskqltlv8/fattach/PHK5R1756689404983.docx"]
            }
        }
    )


def validate_docxgen_request(data: dict) -> DocxgenRequest:
    """
    验证docx生成请求数据

    Args:
        data (dict): 请求数据字典

    Returns:
        DocxgenRequest: 验证通过的请求模型实例

    Raises:
        ValidationError: 当数据验证失败时
    """
    return DocxgenRequest(**data)


def validate_coze_doc_request(data: dict) -> CozeDocRequest:
    """
    验证Coze文档处理请求数据

    Args:
        data: 请求数据字典

    Returns:
        CozeDocRequest: 验证后的请求对象

    Raises:
        ValidationError: 数据验证失败时抛出
    """
    return CozeDocRequest(**data)


class WsinstRequest(BaseModel):
    """
    节水型单位文档生成请求数据模型

    用于验证创建节水型单位文档生成任务的请求参数。
    """

    # 必需字段
    TaskKey: Annotated[str, Field(
        min_length=1,
        max_length=100,
        pattern=r'^[a-zA-Z0-9_-]+$',
        description="任务唯一标识符，只能包含字母、数字、下划线和连字符，长度1-100字符",
        examples=["wsinst_task_20240101_001"]
    )]

    Callback: HttpUrl = Field(
        ...,
        description="回调URL地址，用于任务完成后的通知",
        examples=["http://127.0.0.1:5577/api/v1/aihub/wsinst/new/callback"]
    )

    Contents: str = Field(
        ...,
        min_length=0,
        description="docx文档文字内容，JSON字符串格式",
        examples=["7、自评报告"]
    )

    Files: List[Dict[str, str]] = Field(
        ...,
        description="docx文档附件列表，包含name和url字段的对象数组",
        examples=[{"name": "课程大纲", "url": "http://example.com/image1.png"}, {"name": "photo", "url": "http://example.com/image2.jpg"}]
    )

    @field_validator('TaskKey')
    @classmethod
    def validate_task_key(cls, v: str) -> str:
        """
        验证TaskKey格式
        """
        if not v:
            raise ValueError('TaskKey不能为空')

        if not v.replace('_', '').replace('-', '').isalnum():
            raise ValueError('TaskKey只能包含字母、数字、下划线和连字符')

        if len(v) > 100:
            raise ValueError('TaskKey长度不能超过100个字符')

        return v

    @field_validator('Callback')
    @classmethod
    def validate_callback_url(cls, v: HttpUrl) -> HttpUrl:
        """
        验证回调URL格式
        """
        if not v:
            raise ValueError('Callback不能为空')

        # 转换为字符串进行进一步验证
        url_str = str(v)

        # 检查是否为HTTP或HTTPS协议
        if not url_str.startswith(('http://', 'https://')):
            raise ValueError('Callback必须是HTTP或HTTPS协议的URL')

        return v

    @field_validator('Contents')
    @classmethod
    def validate_contents(cls, v: str) -> str:
        """
        验证Contents格式
        """
        # 移除空字符串检查，允许空字符串
        return v.strip() if v else ""

    @field_validator('Files')
    @classmethod
    def validate_files(cls, v: List[Dict[str, str]]) -> List[Dict[str, str]]:
        """
        验证Files格式
        """

        # 允许空列表, 对于所有假值(例如空列表、None等)都返回空列表
        if not v:
            return []

        if not isinstance(v, list):
            raise ValueError('Files必须是列表格式')

        for file in v:
            if not isinstance(file, dict):
                raise ValueError('Files数组中的每个元素必须是对象')

            if 'name' not in file or not file['name']:
                raise ValueError('Files数组中的每个对象必须包含name字段且不能为空')

            if 'url' not in file or not file['url']:
                raise ValueError('Files数组中的每个对象必须包含url字段且不能为空')

            # 验证url格式
            url_str = file['url']
            if not url_str.startswith(('http://', 'https://')):
                raise ValueError('Files数组中的url必须是HTTP或HTTPS协议的URL')

        return v

    model_config = ConfigDict(
        # 允许使用字段别名
        populate_by_name=True,
        # 验证赋值
        validate_assignment=True,
        # 使用枚举值而不是枚举名称
        use_enum_values=True,
        # JSON编码配置
        json_encoders={
            # 可以在这里添加自定义编码器
        },
        # 示例配置
        json_schema_extra={
            "example": {
                "TaskKey": "bo9fe288610ead25",
                "Callback": "http://127.0.0.1:5577/api/v1/aihub/wsinst/new/callback",
                "Contents": "7、自评报告",
                "Files": [{"name": "课程大纲", "url": "http://example.com/image1.png"}, {"name": "photo", "url": "http://example.com/image2.jpg"}]
            }
        }
    )

def validate_wsinst_request(data: dict) -> WsinstRequest:
    """
    验证节水型单位文档生成请求数据

    Args:
        data (dict): 请求数据字典

    Returns:
        WsinstRequest: 验证通过的请求模型实例

    Raises:
        ValidationError: 当数据验证失败时
    """
    return WsinstRequest(**data)
