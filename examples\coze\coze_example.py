import os
import time
import json
import requests
import mimetypes
from typing import List, Dict, Optional, Tuple

# 配置常量
TOKEN = 'pat_atGSG7ByTHPEBPDfSjFdhcucsGoGJBOvt9H70k73qjiH6GkYNrRoNjMwvpeBQQmo'
BOT_ID_1 = "7545026832061055022"  # 第一个智能体
BOT_ID_2 = "7545030128452452406"  # 第二个智能体
USER_ID = "123456789"
FILE_UPLOAD_URL = "https://api.coze.cn/v1/files/upload"
CHAT_URL = "https://api.coze.cn/v3/chat"
MESSAGE_LIST_URL = "https://api.coze.cn/v3/chat/message/list"

# 图片文件扩展名
IMAGE_EXTENSIONS = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.tiff', '.svg'}


class FileProcessor:
    def __init__(self, token: str, user_id: str):
        self.token = token
        self.user_id = user_id
        self.headers = {
            'Authorization': f'Bearer {token}',
            'Content-Type': 'application/json'
        }

    def upload_file(self, file_path: str) -> Optional[str]:
        """上传单个文件并返回文件ID"""
        try:
            file_name = os.path.basename(file_path)
            mime_type, _ = mimetypes.guess_type(file_path)
            if not mime_type:
                mime_type = 'application/octet-stream'

            with open(file_path, 'rb') as f:
                files = [('file', (file_name, f, mime_type))]
                upload_headers = {'Authorization': f'Bearer {self.token}'}

                response = requests.post(
                    FILE_UPLOAD_URL,
                    headers=upload_headers,
                    files=files
                )

            if response.status_code != 200:
                print(f"上传失败: {response.status_code} - {response.text}")
                return None

            result = response.json()
            if result.get('code') != 0:
                print(f"API错误: {result.get('msg')}")
                return None

            file_id = result['data']['id']
            print(f"成功上传: {file_name} -> ID: {file_id}")
            return file_id

        except Exception as e:
            print(f"上传过程中出错: {str(e)}")
            return None

    def upload_folder_files(self, folder_path: str) -> List[Dict[str, str]]:
        """上传文件夹中的所有文件并返回文件信息列表（包含类型和ID）"""
        file_infos = []

        if not os.path.isdir(folder_path):
            print(f"无效的文件夹: {folder_path}")
            return file_infos

        print(f"开始上传文件夹: {folder_path}")
        for filename in os.listdir(folder_path):
            file_path = os.path.join(folder_path, filename)
            if os.path.isfile(file_path):
                file_id = self.upload_file(file_path)
                if file_id:
                    # 判断文件类型
                    file_ext = os.path.splitext(filename)[1].lower()
                    file_type = "image" if file_ext in IMAGE_EXTENSIONS else "file"

                    file_infos.append({
                        "type": file_type,
                        "file_id": file_id
                    })

        print(f"共上传 {len(file_infos)} 个文件")
        return file_infos

    def send_chat_request(self, bot_id: str, content: any, content_type: str = "text") -> Optional[Tuple[str, str]]:
        """发送聊天请求并返回chat_id和conversation_id"""
        # 构建消息内容
        if content_type == "object_string":
            content = json.dumps(content)

        payload = {
            "bot_id": bot_id,
            "user_id": self.user_id,
            "stream": False,
            "auto_save_history": True,
            "additional_messages": [{
                "role": "user",
                "content": content,
                "content_type": content_type
            }]
        }

        try:
            response = requests.post(
                CHAT_URL,
                headers=self.headers,
                data=json.dumps(payload)
            )

            if response.status_code != 200:
                print(f"聊天请求失败: {response.status_code} - {response.text}")
                return None

            result = response.json()
            if result.get('code') != 0:
                print(f"API错误: {result.get('msg')}")
                return None

            data = result['data']
            print(f"聊天请求成功 - chat_id: {data['id']}, conversation_id: {data['conversation_id']}")
            return data['id'], data['conversation_id']

        except Exception as e:
            print(f"发送聊天请求出错: {str(e)}")
            return None

    def get_processed_content(self, chat_id: str, conversation_id: str,
                              max_retries: int = 10, retry_interval: int = 90) -> Optional[str]:
        """获取处理后的内容"""
        print(f"开始获取处理结果，最多重试 {max_retries} 次，每次间隔 {retry_interval} 秒...")

        for attempt in range(max_retries):
            try:
                print(f"\n尝试 {attempt + 1}/{max_retries}...")
                url = f"{MESSAGE_LIST_URL}?chat_id={chat_id}&conversation_id={conversation_id}"
                response = requests.get(url, headers=self.headers)

                if response.status_code != 200:
                    print(f"获取消息失败: {response.status_code} - {response.text}")
                    if attempt < max_retries - 1:
                        print(f"等待 {retry_interval} 秒后重试...")
                        time.sleep(retry_interval)
                    continue

                result = response.json()
                if result.get('code') != 0:
                    print(f"API错误: {result.get('msg')}")
                    if attempt < max_retries - 1:
                        print(f"等待 {retry_interval} 秒后重试...")
                        time.sleep(retry_interval)
                    continue

                messages = result.get('data', [])
                for msg in messages:
                    if 'reasoning_content' in msg:
                        content = msg.get('content', '')
                        if content:
                            print("\033[1;33m成功获取到处理内容\033[0m"),
                            return content

                print(f"未找到包含 reasoning_content 的消息")
                if attempt < max_retries - 1:
                    print(f"等待 {retry_interval} 秒后重试...")
                    time.sleep(retry_interval)

            except Exception as e:
                print(f"获取消息出错: {str(e)}")
                if attempt < max_retries - 1:
                    print(f"等待 {retry_interval} 秒后重试...")
                    time.sleep(retry_interval)

        print(f"\033[1;31;40m在 {max_retries} 次尝试后未找到处理内容\033[0m")
        return None

    def process_folder(self, folder_path: str, output_file: str = None) -> bool:
        """处理整个文件夹的文件"""
        # 上传文件夹中的所有文件并获取文件信息（包含类型）
        file_infos = self.upload_folder_files(folder_path)
        if not file_infos:
            print("没有文件上传成功，程序终止")
            return False

        # 显示文件类型统计
        image_count = sum(1 for info in file_infos if info["type"] == "image")
        file_count = sum(1 for info in file_infos if info["type"] == "file")
        print(f"文件类型统计: {image_count} 张图片, {file_count} 个文件")

        # 发送聊天请求到第一个智能体
        chat_info = self.send_chat_request(BOT_ID_1, file_infos, "object_string")
        if not chat_info:
            print("第一个智能体聊天请求失败，程序终止")
            return False

        chat_id_1, conversation_id_1 = chat_info

        # 获取第一个智能体的处理结果
        content = self.get_processed_content(chat_id_1, conversation_id_1)
        if not content:
            print("未能获取第一个智能体的处理结果")
            return False

        print("\n第一个智能体处理完成，开始调用第二个智能体...")

        # 发送第一个智能体的输出到第二个智能体
        chat_info_2 = self.send_chat_request(BOT_ID_2, content, "text")
        if not chat_info_2:
            print("第二个智能体聊天请求失败，程序终止")
            return False

        chat_id_2, conversation_id_2 = chat_info_2

        # 获取第二个智能体的处理结果
        final_content = self.get_processed_content(chat_id_2, conversation_id_2)
        if not final_content:
            print("未能获取第二个智能体的处理结果")
            return False

        # 设置输出文件名
        if not output_file:
            folder_name = os.path.basename(folder_path.rstrip('/\\'))
            output_file = f"{folder_name}_processed.txt"

        # 保存结果到文件
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(final_content)
            print(f"\n\033[1;33m处理完成! 结果已保存至: {output_file}\033[0m")
            return True
        except Exception as e:
            print(f"保存文件时出错: {str(e)}")
            return False


def main():
    # 用户输入文件夹路径
    folder_path = input("请输入文件夹绝对路径(或相对路径): ").strip()

    if not os.path.isdir(folder_path):
        print(f"无效的文件夹路径: {folder_path}")
        return

    # 可选：用户输入输出文件名
    output_file = input("请输入输出文件名（可选，直接回车使用默认名称）: ").strip()
    if not output_file:
        output_file = None

    # 创建处理器实例
    processor = FileProcessor(TOKEN, USER_ID)

    # 处理文件夹
    print("\n" + "=" * 50)
    print("开始处理文件夹")
    print("=" * 50)

    success = processor.process_folder(folder_path, output_file)

    if success:
        print("\n\033[1;33m处理完成!\033[0m")
    else:
        print("\n\033[1;31;40m处理失败!\033[0m")


if __name__ == "__main__":
    main()