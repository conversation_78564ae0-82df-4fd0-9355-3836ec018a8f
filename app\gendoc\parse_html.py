from __future__ import annotations

import io
import requests
from bs4 import BeautifulSoup
from docx.shared import Pt, <PERSON><PERSON>, RGBColor
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.oxml.ns import qn
from PIL import Image
from app.core.logger import get_logger

from typing import TYPE_CHECKING
if TYPE_CHECKING:
    from docx.document import Document as DocumentObject

# 初始化日志记录器
logger = get_logger(__name__)

# 本地域名常量
LOCAL_DOMAIN = "http://127.0.0.1:5579"

# 中文字号映射（与utils.py保持一致）
chinese_fontsize = {
    '初号': Pt(42),
    '小初': Pt(36),
    '一号': Pt(26),
    '小一': Pt(24),
    '二号': Pt(22),
    '小二': Pt(18),
    '三号': Pt(16),
    '小三': Pt(15),
    '四号': Pt(14),
    '小四': Pt(12),
    '五号': Pt(10.5),
    '小五': Pt(9),
    '六号': Pt(7.5),
    '小六': Pt(6.5),
    '七号': Pt(5.5),
    '八号': Pt(5),
}

# 段落对齐方式映射（与utils.py保持一致）
align_map = {
    'LEFT': WD_ALIGN_PARAGRAPH.LEFT,
    'CENTER': WD_ALIGN_PARAGRAPH.CENTER,
    'RIGHT': WD_ALIGN_PARAGRAPH.RIGHT,
    'JUSTIFY': WD_ALIGN_PARAGRAPH.JUSTIFY,
}

# 跳过的标签列表
SKIP_TAGS = {'script', 'button', 'style', 'meta', 'link', 'head', 'title'}

def is_real_number(value):
    """检查是否为实数"""
    return isinstance(value, (int, float)) and not isinstance(value, bool)

def normalize_font_size(font_size):
    """标准化字体大小（与utils.py保持一致）"""
    if is_real_number(font_size):
        return Pt(font_size)
    elif font_size in chinese_fontsize.keys():
        return chinese_fontsize.get(font_size, 10.5)
    else:
        return Pt(10.5)

def parse_font_color(color_str):
    """
    解析字体颜色字符串为RGBColor对象
    支持格式: "000000", "#000000", "black" 等
    """
    if not color_str:
        return None

    # 移除 # 前缀
    if color_str.startswith('#'):
        color_str = color_str[1:]

    # 预定义颜色映射
    color_map = {
        'black': '000000',
        'white': 'FFFFFF',
        'red': 'FF0000',
        'green': '00FF00',
        'blue': '0000FF',
        'yellow': 'FFFF00',
        'cyan': '00FFFF',
        'magenta': 'FF00FF'
    }

    # 如果是预定义颜色名称
    if color_str.lower() in color_map:
        color_str = color_map[color_str.lower()]

    # 验证是否为有效的6位十六进制颜色
    if len(color_str) == 6 and all(c in '0123456789ABCDEFabcdef' for c in color_str):
        try:
            # 使用 from_string 方法创建 RGBColor 对象
            return RGBColor.from_string(color_str)
        except ValueError:
            pass

    # 默认返回黑色
    return RGBColor.from_string('000000')

def apply_text_style(run, style_info: dict):
    """应用文本样式"""
    font_name = style_info.get('fontName', 'SimSun')
    font_size = normalize_font_size(style_info.get('fontSize', 10.5))
    font_bold = style_info.get('fontBold', False)
    font_italic = style_info.get('fontItalic', False)
    font_color = style_info.get('fontColor', '000000')  # 默认黑色

    run.font.name = font_name
    run.font.size = font_size
    run.font.bold = font_bold
    run.font.italic = font_italic
    # 设置字体颜色
    if font_color:
        color_obj = parse_font_color(font_color)
        if color_obj:
            run.font.color.rgb = color_obj
    # 设置东亚字体以确保中文字符正确显示
    run._element.rPr.rFonts.set(qn('w:eastAsia'), font_name)

def apply_paragraph_style(paragraph, style_info: dict):
    """应用段落样式"""
    align = align_map.get(style_info.get('align', 'left').upper(), WD_ALIGN_PARAGRAPH.LEFT)
    left_indent = style_info.get('leftIndent', 0)
    line_spacing = style_info.get('lineSpacing', 1.0)
    space_before = style_info.get('spaceBefore', 0)
    space_after = style_info.get('spaceAfter', 0)

    paragraph.alignment = align

    # 设置段落格式
    if left_indent > 0:
        font_size = normalize_font_size(style_info.get('fontSize', 10.5))
        paragraph.paragraph_format.left_indent = int(0.6 * left_indent * font_size.pt)

    if line_spacing != 1.0:
        paragraph.paragraph_format.line_spacing = line_spacing

    if space_before > 0:
        font_size = normalize_font_size(style_info.get('fontSize', 10.5))
        paragraph.paragraph_format.space_before = int(1.2 * space_before * font_size.pt)

    if space_after > 0:
        font_size = normalize_font_size(style_info.get('fontSize', 10.5))
        paragraph.paragraph_format.space_after = int(1.2 * space_after * font_size.pt)

def download_image(src: str) -> io.BytesIO:
    """下载图片并返回字节流"""
    try:
        # 处理本地域名
        if src.startswith('/'):
            src = LOCAL_DOMAIN + src
        elif src.startswith('./') or not src.startswith('http'):
            src = LOCAL_DOMAIN + '/' + src.lstrip('./')

        response = requests.get(src, stream=True, timeout=10)
        response.raise_for_status()
        return io.BytesIO(response.content)
    except Exception as e:
        logger.error(f"下载图片失败 {src}: {e}")
        return None

def process_paragraph_element(doc: DocumentObject, element, style_info: dict):
    """处理段落元素（p, div等）"""
    text = element.get_text().strip()
    if not text:
        return

    paragraph = doc.add_paragraph()
    run = paragraph.add_run(text)

    # 应用样式
    apply_text_style(run, style_info)
    apply_paragraph_style(paragraph, style_info)

def process_heading_element(doc: DocumentObject, element, level: int, style_info: dict):
    """处理标题元素（h1-h6）"""
    text = element.get_text().strip()
    if not text:
        return

    # 构建标题信息字典，调用add_custom_heading来应用模板样式
    heading_info = {
        'type': 'Heading',
        'content': text,
        'Level': level,
        'useTemp': True,  # 使用模板样式
        'fontName': style_info.get('fontName', 'SimHei'),
        'fontSize': style_info.get('fontSize', 16 if level == 1 else 14 if level == 2 else 12),
        'fontBold': style_info.get('fontBold', True),
        'fontItalic': style_info.get('fontItalic', False),
        'align': style_info.get('align', 'left')
    }

    # 调用utils.py中的add_custom_heading函数
    from .utils import add_custom_heading
    add_custom_heading(doc, heading_info)

def process_list_element(doc: DocumentObject, element, style_info: dict, ordered=False):
    """处理列表元素（ul, ol）"""
    list_items = element.find_all('li', recursive=False)

    for i, li in enumerate(list_items):
        text = li.get_text().strip()
        if not text:
            continue

        # 创建列表项段落
        paragraph = doc.add_paragraph()

        # 添加列表标记
        if ordered:
            prefix = f"{i + 1}. "
        else:
            prefix = "• "

        run = paragraph.add_run(prefix + text)

        # 应用样式
        apply_text_style(run, style_info)
        apply_paragraph_style(paragraph, style_info)

        # 设置列表缩进
        font_size = normalize_font_size(style_info.get('fontSize', 10.5))
        paragraph.paragraph_format.left_indent = int(0.6 * 2 * font_size.pt)  # 2字符缩进

def process_image_element(doc: DocumentObject, element, style_info: dict):
    """处理图片元素"""
    src = element.get('src')
    if not src:
        return

    image_stream = download_image(src)
    if image_stream:
        try:
            # 获取图片原始尺寸
            image_stream.seek(0)  # 重置流位置
            with Image.open(image_stream) as img:
                # 获取图片的像素尺寸
                width_px, height_px = img.size

                # 假设DPI为96（常见的屏幕DPI），将像素转换为英寸
                dpi = 96
                width_inches = width_px / dpi
                height_inches = height_px / dpi

                # 判断图片尺寸并调整
                max_size_inches = 5.0  # 最大尺寸限制为5英寸

                if width_inches <= max_size_inches and height_inches <= max_size_inches:
                    # 如果长宽都小于等于5英寸，使用原始尺寸
                    target_width = width_inches
                    target_height = height_inches
                    logger.info(f"图片尺寸在限制范围内，使用原始尺寸: {width_inches:.2f}x{height_inches:.2f}英寸")
                else:
                    # 如果长或宽超过8英寸，按比例缩放
                    if width_inches > height_inches:
                        # 宽度是较大边，以宽度为准缩放
                        scale_factor = max_size_inches / width_inches
                        target_width = max_size_inches
                        target_height = height_inches * scale_factor
                    else:
                        # 高度是较大边，以高度为准缩放
                        scale_factor = max_size_inches / height_inches
                        target_height = max_size_inches
                        target_width = width_inches * scale_factor

                    logger.info(f"图片尺寸超出限制，从 {width_inches:.2f}x{height_inches:.2f}英寸 缩放到 {target_width:.2f}x{target_height:.2f}英寸")

                # 重置流位置以供docx使用
                image_stream.seek(0)

                # 添加图片，使用计算出的尺寸
                doc.add_picture(image_stream, width=Inches(target_width), height=Inches(target_height))

        except Exception as e:
            logger.error(f"插入图片失败: {e}")
            # 添加占位文本
            paragraph = doc.add_paragraph()
            run = paragraph.add_run(f"[图片无法加载: {src}]")
            apply_text_style(run, style_info)
    else:
        # 添加占位文本
        paragraph = doc.add_paragraph()
        run = paragraph.add_run(f"[图片无法加载: {src}]")
        apply_text_style(run, style_info)

def safe_int_conversion(value, default=1):
    """安全地将值转换为整数，处理错误的属性值"""
    if value is None:
        return default

    # 如果已经是整数，直接返回
    if isinstance(value, int):
        return value

    # 转换为字符串并尝试解析
    str_value = str(value).strip()

    # 处理空字符串
    if not str_value:
        return default

    # 尝试直接转换为整数
    try:
        return int(str_value)
    except ValueError:
        # 如果转换失败，记录警告并返回默认值
        logger.warning(f"无法将属性值 '{str_value}' 转换为整数，使用默认值 {default}")
        return default

def process_table_element(doc: DocumentObject, element, style_info: dict):
    """处理表格元素（参考pydocx.py的实现）"""
    # 检查是否存在caption元素
    caption = element.find('caption')
    if caption:
        caption_text = caption.get_text().strip()
        if caption_text:
            # 添加表格标题段落，使用表格caption样式
            caption_paragraph = doc.add_paragraph()
            caption_run = caption_paragraph.add_run(caption_text)
            # 应用表格caption样式
            apply_text_style(caption_run, style_info)
            # 设置caption样式：居中对齐，可能需要特殊的字体样式
            caption_paragraph.alignment = WD_ALIGN_PARAGRAPH.LEFT
            # 如果需要特殊的caption样式，可以在这里设置
            caption_run.font.bold = True  # caption通常使用粗体

    rows_data = element.find_all('tr')
    if not rows_data:
        return

    # 确定表格的最大列数 - 需要考虑colspan
    max_cols = 0
    for tr in rows_data:
        col_count = 0
        for td in tr.find_all(['td', 'th']):
            colspan = safe_int_conversion(td.get('colspan'), 1)
            col_count += colspan
        max_cols = max(max_cols, col_count)

    if max_cols == 0:
        return

    # 创建Word表格
    table = doc.add_table(rows=len(rows_data), cols=max_cols)
    table.style = 'Table Grid'  # 使用带边框的样式

    # 创建虚拟网格来跟踪已合并的单元格
    grid = [[None for _ in range(max_cols)] for _ in range(len(rows_data))]

    for r_idx, tr in enumerate(rows_data):
        c_idx = 0
        for td in tr.find_all(['td', 'th']):
            # 找到当前行中第一个可用的单元格
            while c_idx < max_cols and grid[r_idx][c_idx] is not None:
                c_idx += 1

            if c_idx >= max_cols:
                break

            rowspan = safe_int_conversion(td.get('rowspan'), 1)
            colspan = safe_int_conversion(td.get('colspan'), 1)

            # 填充文本
            cell = table.cell(r_idx, c_idx)
            cell.text = td.get_text().strip()

            # 合并单元格
            if rowspan > 1 or colspan > 1:
                try:
                    end_row = min(r_idx + rowspan - 1, len(rows_data) - 1)
                    end_col = min(c_idx + colspan - 1, max_cols - 1)
                    bottom_right_cell = table.cell(end_row, end_col)
                    cell.merge(bottom_right_cell)
                except Exception as e:
                    logger.warning(f"合并单元格失败 (r:{r_idx}, c:{c_idx}, rowspan:{rowspan}, colspan:{colspan}): {e}")

            # 在虚拟网格中标记被占用的单元格
            for i in range(r_idx, min(r_idx + rowspan, len(rows_data))):
                for j in range(c_idx, min(c_idx + colspan, max_cols)):
                    if i == r_idx and j == c_idx:
                        continue  # 跳过左上角单元格本身
                    grid[i][j] = "merged"  # 标记为已占用

            c_idx += colspan

def process_figure_element(doc: DocumentObject, element, style_info: dict):
    """处理figure元素"""
    # 查找figure中的img元素
    img = element.find('img')
    if img:
        process_image_element(doc, img, style_info)

    # 查找figure中的figcaption元素
    caption = element.find('figcaption')
    if caption:
        text = caption.get_text().strip()
        if text:
            paragraph = doc.add_paragraph()
            run = paragraph.add_run(text)
            apply_text_style(run, style_info)
            paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER  # 图片说明居中

def process_html_element(doc: DocumentObject, element, style_info: dict):
    """递归处理HTML元素"""
    if element.name is None:  # 文本节点
        return

    if element.name.lower() in SKIP_TAGS:
        return

    tag_name = element.name.lower()

    # 处理不同类型的HTML标签
    if tag_name == 'p':
        process_paragraph_element(doc, element, style_info)
    elif tag_name == 'div':
        # div可能包含多个子元素，递归处理
        if element.get_text().strip() and not element.find_all():
            # 如果div只包含文本，作为段落处理
            process_paragraph_element(doc, element, style_info)
        else:
            # 递归处理子元素
            for child in element.children:
                if hasattr(child, 'name'):
                    process_html_element(doc, child, style_info)
    elif tag_name in ['h1', 'h2', 'h3', 'h4', 'h5', 'h6']:
        level = int(tag_name[1])  # 提取数字
        process_heading_element(doc, element, level, style_info)
    elif tag_name == 'ul':
        process_list_element(doc, element, style_info, ordered=False)
    elif tag_name == 'ol':
        process_list_element(doc, element, style_info, ordered=True)
    elif tag_name == 'img':
        process_image_element(doc, element, style_info)
    elif tag_name == 'table':
        process_table_element(doc, element, style_info)
    elif tag_name == 'figure':
        process_figure_element(doc, element, style_info)
    else:
        # 对于其他标签，递归处理子元素
        for child in element.children:
            if hasattr(child, 'name'):
                process_html_element(doc, child, style_info)

def parse_html_dom(doc: DocumentObject, info: dict) -> DocumentObject:
    """解析HTML DOM并转换为DOCX文档

    Args:
        doc: DOCX文档对象
        info: 包含HTML内容和样式信息的字典
              格式: {
                  "type": "HTML",
                  "content": "<div><p>这是一个段落</p></div>",
                  "fontName": "SimSun",
                  "fontSize": 10.5,
                  "fontBold": false,
                  "fontItalic": false,
                  "align": "Left",
                  "leftIndent": 2,
                  "lineSpacing": 1.5,
                  "spaceBefore": 0.5,
                  "spaceAfter": 0.5,
                  "orient": "portrait"  # 可选，文档方向
              }

    Returns:
        DocumentObject: 更新后的文档对象
    """
    try:
        # 设置文档方向（如果指定）
        if info.get('orient'):
            from .utils import init_doc_orientation
            init_doc_orientation(doc, info)

        # Document对象基于template.docx创建时包含默认内容，需要全部清空
        # 在写入内容之前清空文档中的所有内容

        # 清空所有段落
        while len(doc.paragraphs) > 0:
            paragraph = doc.paragraphs[0]
            # 删除段落
            p = paragraph._element
            p.getparent().remove(p)
            paragraph._p = paragraph._element = None

        # 清空所有表格
        while len(doc.tables) > 0:
            table = doc.tables[0]
            # 删除表格
            t = table._element
            t.getparent().remove(t)
            table._element = None

        # 清空所有其他内容元素（如图片、形状等）
        # 获取文档主体元素
        body = doc._body._element
        # 删除所有子元素，但保留基本结构
        for element in list(body):
            # 保留sectPr（节属性）元素，删除其他所有内容
            if element.tag.endswith('sectPr'):
                continue
            body.remove(element)

        # 获取HTML内容
        html_content = info.get('content', '')
        if not html_content:
            logger.warning("HTML内容为空")
            return doc

        # 解析HTML
        soup = BeautifulSoup(html_content, 'html.parser')

        # 提取样式信息
        style_info = {
            'fontName': info.get('fontName', 'SimSun'),
            'fontSize': info.get('fontSize', 10.5),
            'fontBold': info.get('fontBold', False),
            'fontItalic': info.get('fontItalic', False),
            'align': info.get('align', 'Left'),
            'leftIndent': info.get('leftIndent', 0),
            'lineSpacing': info.get('lineSpacing', 1.0),
            'spaceBefore': info.get('spaceBefore', 0),
            'spaceAfter': info.get('spaceAfter', 0)
        }

        # 处理HTML元素
        # 如果HTML内容有根元素，直接处理；否则处理所有顶级元素
        root_elements = soup.find_all(recursive=False)
        if not root_elements:
            # 可能是纯文本或者单个元素
            if soup.string:
                paragraph = doc.add_paragraph()
                run = paragraph.add_run(soup.string.strip())
                apply_text_style(run, style_info)
                apply_paragraph_style(paragraph, style_info)
        else:
            for element in root_elements:
                process_html_element(doc, element, style_info)

        logger.info("HTML解析完成")

    except Exception as e:
        logger.error(f"HTML解析失败: {e}")
        # 添加错误信息到文档
        error_paragraph = doc.add_paragraph()
        error_run = error_paragraph.add_run(f"HTML解析失败: {str(e)}")
        error_run.font.name = 'SimSun'
        error_run.font.size = Pt(10.5)

    return doc
