# -*- coding: utf-8 -*-
"""
API响应数据模型

定义所有API端点的响应数据模型，提供统一的响应格式。
"""

from typing import Optional, Any, Dict
from pydantic import BaseModel, Field, ConfigDict


class BaseResponse(BaseModel):
    """
    基础响应模型

    所有API响应的基础结构。
    """

    status: str = Field(
        description="响应状态",
        examples=["success", "error"]
    )

    message: Optional[str] = Field(
        default=None,
        description="响应消息"
    )

    request_id: Optional[str] = Field(
        default=None,
        description="请求ID"
    )

    response_time: Optional[str] = Field(
        default=None,
        description="响应时间"
    )


class SuccessResponse(BaseResponse):
    """
    成功响应模型

    API调用成功时的响应格式。
    """

    status: str = Field(default="success", description="成功状态")
    data: Optional[Any] = Field(default=None, description="响应数据")


class ErrorResponse(BaseResponse):
    """
    错误响应模型

    API调用失败时的响应格式。
    """

    status: str = Field(default="error", description="错误状态")
    error_code: Optional[str] = Field(default=None, description="错误代码")
    error_details: Optional[Dict[str, Any]] = Field(default=None, description="错误详情")


class HealthResponse(BaseModel):
    """
    健康检查响应模型
    """

    status: str = Field(description="服务状态")
    service: str = Field(description="服务名称")
    version: str = Field(description="服务版本")
    timestamp: str = Field(description="时间戳")


class TingwuResponse(BaseModel):
    """
    千义听悟API响应模型
    """

    Code: str = Field(description="响应代码")
    Message: str = Field(description="响应消息")
    RequestId: str = Field(description="请求ID")
    Data: Optional[Dict[str, Any]] = Field(default=None, description="响应数据")


class ChatResponse(BaseModel):
    """
    聊天API响应模型
    """

    status: str = Field(description="响应状态")
    content: str = Field(description="聊天内容")
    request_id: str = Field(description="请求ID")
    response_time: str = Field(description="响应时间")


class DocxgenResponse(BaseModel):
    """
    Docx生成响应模型
    """

    TaskKey: str = Field(description="任务唯一标识符")
    RuntimeStatus: str = Field(description="程序运行状态")
    TaskStatus: str = Field(description="任务状态")
    Data: Optional[Dict[str, Any]] = Field(default=None, description="响应数据")


class CozeDocResponse(BaseModel):
    """
    Coze文档处理响应模型
    """
    Code: int = Field(description="响应代码")
    Message: str = Field(description="响应消息")
    TaskKey: str = Field(description="任务唯一标识符")
    RuntimeStatus: str = Field(description="程序运行状态，SUCCESS或FAILED")
    TaskStatus: str = Field(description="任务状态，CREATED、ONGOING或COMPLETED")
    Data: Optional[Dict[str, Any]] = Field(default=None, description="响应数据")


class WsinstResponse(BaseModel):
    """
    节水型单位文档生成响应模型

    用于节水型单位文档生成任务的响应数据结构。
    """

    TaskKey: str = Field(
        description="任务唯一标识符",
        examples=["bo9fe288610ead25"]
    )

    RuntimeStatus: str = Field(
        description="程序运行状态",
        examples=["SUCCESS", "FAILED"]
    )

    TaskStatus: str = Field(
        description="任务状态",
        examples=["CREATED", "ONGOING", "COMPLETED"]
    )

    Data: Optional[Dict[str, Any]] = Field(
        default=None,
        description="响应数据",
        examples=[{"fileUrl": "http://example.com/output.docx"}]
    )

    model_config = ConfigDict(
        # 允许使用字段别名
        populate_by_name=True,
        # 验证赋值
        validate_assignment=True,
        # 使用枚举值而不是枚举名称
        use_enum_values=True,
        # JSON编码配置
        json_encoders={
            # 可以在这里添加自定义编码器
        },
        # 示例配置
        json_schema_extra={
            "example": {
                "TaskKey": "bo9fe288610ead25",
                "RuntimeStatus": "SUCCESS",
                "TaskStatus": "CREATED",
                "Data": {"fileUrl": "http://example.com/output.docx"}
            }
        }
    )
